# Use a multi-arch Debian base image
FROM --platform=linux/amd64 debian:bullseye-slim

# Install necessary packages
RUN apt update && apt install -y build-essential curl git

# Install Go
RUN curl -OL https://golang.org/dl/go1.23.4.linux-amd64.tar.gz && \
    rm -rf /usr/local/go && \
    tar -C /usr/local -xzf go1.23.4.linux-amd64.tar.gz && \
    rm go1.23.4.linux-amd64.tar.gz

# Set PATH for Go
ENV PATH="/usr/local/go/bin:${PATH}"

WORKDIR /app

# Копируем зависимости и файлы
COPY SDK.7z main.go users.json Keys bot go.mod go.sum .

# Устанавливаем зависимости, компилируем приложение и подготавливаем окружение
RUN apt update && apt install -y libltdl7 libpcsclite1 p7zip-full unzip && apt-get install nano && \
    7z x SDK.7z && \
    cp SDK\ 2.0/C/Linux/C/libs/v2.0.13/C_libs.zip C_libs.zip && \
    unzip C_libs.zip && \
    cp C_libs/libkalkancryptwr-64.so.2.0.13 libkalkancryptwr-64.so && \
    ./install_production.sh && \
    ./install_test.sh && \
    cd /app && cp libkalkancryptwr-64.so /usr/lib/ && ldconfig && \
    chmod 755 /usr/lib/libkalkancryptwr-64.so && \
    go build -o main main.go && \
    rm -rf C_libs.zip SDK.7z SDK 2.0 \

    
# Указываем команду запуска
CMD ["./main"]
