<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Өтініштер журналы</title>
    <link href="/Content/css?v=e_yMk_6cSAs8uU_ry0t63Z96uzWBNVnLGRHEI5G5SXY1" rel="stylesheet"/>

    <link href="/Content/ALMATY/localcss?v=00Xu8A8VAK3bEA0ia0mBTBnGVAjD1-yHyUcljy5MANk1" rel="stylesheet"/>

    
    <link href="/Content/devextreme?v=FWhSwrMiLUoTuAkoGbAJLONP2bcENQoK7q4CrFc8dIM1" rel="stylesheet"/>

    <style>
        .modal-dialog {
            width: 850px;
            margin: 30px auto;
        }
        .modal-body {
            position: relative;
            padding: 0px;
        }
    </style>

    <script src="/Scripts/lib/jquery/jquery-2.2.0.min.js"></script>

    <script src="/Scripts/lib/survey/knockout-min.js"></script>
    <script src="/Scripts/lib/survey/survey.ko.min.js"></script>
    <link href="/Content/style/survey/modern.css" rel="stylesheet" />

    <script src="/bundles/requirejs?v=ZiNE-9H4frQl8QLqh_c37dmEElcDt7-yLl6cBVocYzQ1"></script>

</head>
<body>

    <div id="wrapper">
        

<header class="header">
    <nav class="navbar navbar-default navbar-static-top header-is-prod" style="padding: 10px 0px 10px 0px">
        <div class="container-fluid EmplayeeHeader">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navigation" aria-expanded="false">
                    <span class="sr-only">Навигацияны ауыстыру</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="/Common/Home">
                    <img class="imgLogo" src="/Content/City/ALMATY/images/logo.svg" />
                    <span class="hidden-xs" style="font-size:16px; margin-left: 10px">Алматы қаласы білім басқармасы</span>
                </a>
            </div>
            
<div class="collapse navbar-collapse" id="navigation">
    <ul class="nav navbar-nav navbar-right">
<form action="/Common/Account/LogOff" class="login-right" id="logoutForm" method="post"></form>            <li>
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown">УСМАНОВА АЙНУРА (************) <b class="caret"></b></a>
                        <ul class="dropdown-menu">
                            <li><a href="/Common/Account/Edit">Деректерге өзгерістер енгізу</a></li>
                            <li><a href="/Common/Account/UserActionsHistory">Жүйеге кіру тарихы</a></li>
                        </ul>

            </li>
            <li>
                <a href="javascript:document.getElementById('logoutForm').submit()" class="logout-button">
                    <img class="LogoutImg" src="/Content/images/enter.svg" style="color: darkgrey"/>
                    ШЫҒУ
                </a>
            </li>
        <li>
            <a href="/Common/Resources/ChangeCulture?culture=RU" class="lang-link">
                Русский
            </a>
        </li>
        <li>
            <a href="/Common/Resources/ChangeCulture?culture=KK" class="lang-link">Қазақша</a>
        </li>
    </ul>
</div>
        </div>
    </nav>
</header>

        <div id="content">
            

<div class="page-header">
    <div class="container-fluid">
        <img src="/Content/images/cubes.svg" />
        <h1>Өтініштер журналы</h1>
    </div>
</div>

<div class="container-fluid">
    <div class="content-area">
        <div id="data-grid-demo">
            <div id="gridContainer" data-bind="dxDataGrid: dataGridOptions"></div>
        </div>
    </div>
</div>

<div class="hidden">
    <div id="components"></div>
    <div id="detail-template">
        <div class="request-item-dropdown">
            <ul class="nav nav-tabs">
                <li class="active">
                    <a data-toggle="tab" data-bind="attr : { href : applicantIndetifierAttr.hrefAttr},text:_t.GroupRequestInfo"></a>
                </li>
                <li>
                    <a data-toggle="tab" data-bind="attr : { href :historyIdentifierAttr.hrefAttr},text:_t.ChangeHistory"></a>
                <li>
                    <a data-toggle="tab" data-bind="attr : { href :attachmentIdentifierAttr.hrefAttr}, text:_t.GeneralDocuments"></a>
                </li>
            </ul>
            <div style="margin-top: 10px;">
            </div>
            <div class="tab-content">
                <div class="tab-pane active" data-bind="attr:{id : applicantIndetifierAttr.idAttr}">
                    <!--ko with: applicantModel-->
                    <applicant-view params="model:$data"><div class="alert alert-info text-center" data-bind="text:_t.PleaseWait"></div></applicant-view>
                    <!--/ko-->
                    <!--ko with: childModel-->
                    <child-view params="model:$data"></child-view>
                    <!--/ko-->
                    <!--ko with:baseRequestModel-->
                    <base-request-view params="model:$data"></base-request-view>
                    <!--/ko-->
                </div>
                <div class="tab-pane" data-bind="attr:{id : historyIdentifierAttr.idAttr}">
                    <!--ko with: historyModel-->
                    <history-view params="model:$data"></history-view>
                    <!--/ko-->
                </div>
                <div class="tab-pane" data-bind="attr:{id:attachmentIdentifierAttr.idAttr}">
                    <!--ko with :attachmentModel-->
                    <grid-attachment-view params="model:$data"></grid-attachment-view>
                    <!--/ko-->
                </div>
            </div>
            <div style="margin-top: 30px;" class="col-md-12">
                <async-grid-transitions params="onInit:transitionsInit,Id:id"></async-grid-transitions>
            </div>
        </div>
    </div>
</div>



        </div>
        
<footer class="footer">

    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-3 PColSm2">
                <p>ЭЛЕКТРОНДЫҚ ҚЫЗМЕТТЕР ПОРТАЛЫ</p>
            </div>
            <div class="col-sm-2">
            </div>


                <div class="col-sm-7">
                <ul class="social-links pull-right">
                    
                </ul>
            </div>
        </div>
    </div>
</footer>

    </div>
    
    <script>
        require(['vendor','resources', 'features'],
        function () {

        require(['knockout', 'jquery', 'services/dayCareQueue-client-grid'], function (ko, $, EmployeeGrid) {
            var model = new EmployeeGrid();
            ko.applyBindings(model, document.getElementById('data-grid-demo'));
        })
            });
    </script>
    <!-- Yandex.Metrika counter -->
    <script type="text/javascript">(function (d, w, c) { (w[c] = w[c] || []).push(function () { try { w.yaCounter47520733 = new Ya.Metrika2({ id: 47520733, clickmap: true, trackLinks: true, accurateTrackBounce: true }); } catch (e) { } }); var n = d.getElementsByTagName("script")[0], s = d.createElement("script"), f = function () { n.parentNode.insertBefore(s, n); }; s.type = "text/javascript"; s.async = true; s.src = "https://mc.yandex.ru/metrika/tag.js"; if (w.opera == "[object Opera]") { d.addEventListener("DOMContentLoaded", f, false); } else { f(); } })(document, window, "yandex_metrika_callbacks2");</script> <noscript><div><img src="https://mc.yandex.ru/watch/47520733" style="position:absolute; left:-9999px;" alt="" /></div></noscript> <!-- /Yandex.Metrika counter -->
</body>
</html>
