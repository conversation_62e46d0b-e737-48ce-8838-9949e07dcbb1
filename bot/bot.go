package bot

import (
	"encoding/json"
	"fmt"
	"log"
	"net"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/valyala/fasthttp"
)

// StartBot calls functions. Optimized for competitive booking at 6 AM.
func StartBot(host string) {
	file, err := os.ReadFile("./users.json")
	if err != nil {
		log.Fatalf("Error reading config file: %v", err)
	}
	var users []User
	if err := json.Unmarshal(file, &users); err != nil {
		log.Fatalf("Error parsing JSON data: %v", err)
	}

	// Configure the HTTP client with aggressive timeouts for speed
	hostClient := &fasthttp.HostClient{
		Addr:  host + ":443",
		IsTLS: true,
		Dial: func(addr string) (net.Conn, error) {
			return fasthttp.DialTimeout(addr, 5*time.Second) // Faster timeout
		},
		MaxConnDuration:     30 * time.Second,
		MaxIdleConnDuration: 10 * time.Second,
	}

	log.Printf("🚀 Starting competitive booking bot for %d users", len(users))

	// PHASE 1: Pre-authenticate all users (do this BEFORE 6 AM)
	log.Println("📋 Phase 1: Pre-authenticating all users...")
	if err := preAuthenticateUsers(users, hostClient); err != nil {
		log.Fatalf("Pre-authentication failed: %v", err)
	}

	// PHASE 2: Wait until exactly 6 AM (or target time)
	log.Println("⏰ Phase 2: Waiting for booking time...")
	waitUntilBookingTime()

	// PHASE 3: Rapid concurrent booking attempts
	log.Println("🎯 Phase 3: BOOKING STARTED! All users attempting to book...")
	performConcurrentBooking(users)
}

// preAuthenticateUsers logs in all users before the booking time to save time
func preAuthenticateUsers(users []User, hostClient *fasthttp.HostClient) error {
	var wg sync.WaitGroup
	errCh := make(chan error, len(users))
	successCount := 0

	for i := range users {
		users[i].ID = i + 1
		users[i].Client = NewCookieClient(hostClient)

		wg.Add(1)
		go func(u *User) {
			defer wg.Done()
			if err := u.Login(); err != nil {
				errCh <- fmt.Errorf("user %d pre-auth failed: %v", u.ID, err)
			} else {
				log.Printf("✅ User %d pre-authenticated successfully", u.ID)
				successCount++
			}
		}(&users[i])
	}

	wg.Wait()
	close(errCh)

	// Report any errors but don't fail completely
	for err := range errCh {
		log.Printf("⚠️  Pre-auth error: %v", err)
	}

	log.Printf("📊 Pre-authentication complete: %d/%d users ready", successCount, len(users))
	return nil
}

// waitUntilBookingTime waits until exactly 6:00 AM (or specified time)
func waitUntilBookingTime() {
	now := time.Now()

	// Target time: 6:00 AM today (or tomorrow if it's already past 6 AM)
	target := time.Date(now.Year(), now.Month(), now.Day(), 6, 0, 0, 0, now.Location())
	if now.After(target) {
		target = target.Add(24 * time.Hour) // Tomorrow at 6 AM
	}

	duration := target.Sub(now)
	log.Printf("⏰ Waiting %v until booking time (%s)", duration, target.Format("15:04:05"))

	// For testing, you can uncomment this to start immediately:
	log.Println("🧪 TESTING MODE: Starting immediately")
	return

	time.Sleep(duration)
	log.Println("🚨 BOOKING TIME REACHED! Starting in 3... 2... 1...")
	time.Sleep(1 * time.Second)
}

// performConcurrentBooking attempts booking with optimized timing strategy
func performConcurrentBooking(users []User) {
	var wg sync.WaitGroup
	results := make(chan string, len(users))

	log.Printf("🚀 Starting staggered booking for %d users to optimize signing mutex...", len(users))

	// OPTIMIZATION: Prioritize users with request_id (they have active applications)
	// Users with request_id go first, then others
	priorityUsers := make([]User, 0)
	regularUsers := make([]User, 0)

	for _, user := range users {
		if user.RequestID != "" {
			priorityUsers = append(priorityUsers, user)
		} else {
			regularUsers = append(regularUsers, user)
		}
	}

	// Combine: priority users first, then regular users
	orderedUsers := append(priorityUsers, regularUsers...)
	log.Printf("📋 User priority: %d priority users, %d regular users", len(priorityUsers), len(regularUsers))

	// OPTIMIZATION: Stagger the start times slightly to reduce mutex contention
	// Users start 10ms apart to spread out the signing bottleneck
	for i := range orderedUsers {
		wg.Add(1)
		go func(u *User, startDelay time.Duration) {
			defer wg.Done()

			// Small stagger to reduce signing mutex contention
			if startDelay > 0 {
				time.Sleep(startDelay)
			}

			start := time.Now()
			if err := u.AttemptGardenBooking(); err != nil {
				duration := time.Since(start)
				results <- fmt.Sprintf("❌ User %d failed in %v: %v", u.ID, duration, err)
			} else {
				duration := time.Since(start)
				results <- fmt.Sprintf("🎉 User %d SUCCESS in %v!", u.ID, duration)
			}
		}(&orderedUsers[i], time.Duration(i)*10*time.Millisecond)
	}

	wg.Wait()
	close(results)

	// Report all results
	log.Println("📊 BOOKING RESULTS:")
	successCount := 0
	for result := range results {
		log.Println(result)
		if strings.Contains(result, "SUCCESS") {
			successCount++
		}
	}

	log.Printf("🏆 Final Score: %d/%d users successfully booked!", successCount, len(users))
}

func BotAction(u *User, hc *fasthttp.HostClient) error {
	if err := u.Login(); err != nil {
		return fmt.Errorf("login ❌ %v", err)
	}

	waitUntilTargetTime(u.ID * 100)
	if err := u.AttemptGardenBooking(); err != nil {
		return fmt.Errorf("booking ❌ %v", err)
	}
	return nil
}
