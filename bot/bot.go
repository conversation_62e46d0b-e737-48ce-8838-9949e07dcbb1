package bot

import (
	"encoding/json"
	"fmt"
	"log"
	"net"
	"os"
	"sync"
	"time"

	"github.com/valyala/fasthttp"
)

// StartBot calls functions. It is concurrently safe.
func StartBot(host string) {
	file, err := os.ReadFile("./users.json")
	if err != nil {
		log.Fatalf("Error reading config file: %v", err)
	}
	var users []User
	if err := json.Unmarshal(file, &users); err != nil {
		log.Fatalf("Error parsing JSON data: %v", err)
	}

	// Configure the HTTP client
	hostClient := &fasthttp.HostClient{
		Addr:  host + ":443",
		IsTLS: true,
		Dial: func(addr string) (net.Conn, error) {
			return fasthttp.DialTimeout(addr, 15*time.Second)
		},
	}

	var wg sync.WaitGroup
	errCh := make(chan error, len(users))

	// Start a goroutine for each user
	wg.Add(len(users))
	for i := range users {
		users[i].ID = i + 1
		users[i].Client = NewCookieClient(hostClient)

		go func(u *User) {
			defer wg.Done()
			if err := BotAction(u, hostClient); err != nil {
				errCh <- fmt.Errorf("user %d - failed: %v", u.ID, err)
			}
		}(&users[i])
	}

	wg.Wait()
	close(errCh)

	// Cleanup: close all clients in the pool
	if clientPool != nil {
		clientPool.CloseAll()
	}

	// Handle errors
	for err := range errCh {
		log.Printf("Error: %v", err)
	}
}

func BotAction(u *User, hc *fasthttp.HostClient) error {
	if err := u.Login(); err != nil {
		return fmt.Errorf("login ❌ %v", err)
	}

	waitUntilTargetTime(u.ID * 100)
	if err := u.AttemptGardenBooking(); err != nil {
		return fmt.Errorf("booking ❌ %v", err)
	}
	return nil
}
