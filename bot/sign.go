package bot

import (
	"fmt"
	"strings"
	"sync"

	"github.com/gokalkan/gokalkan"
)

// KalkanClientPool manages a pool of gokalkan clients for concurrent use
type Kalkan<PERSON>lientPool struct {
	pool chan *gokalkan.Client
	opts []gokalkan.Option
}

// Global client pool instance
var clientPool *KalkanClientPool
var poolOnce sync.Once

// initClientPool initializes the client pool with a specified size
func initClientPool(poolSize int) *KalkanClientPool {
	poolOnce.Do(func() {
		clientPool = &KalkanClientPool{
			pool: make(chan *gokalkan.Client, poolSize),
			opts: gokalkan.OptsProd,
		}

		// Pre-populate the pool with clients
		for i := 0; i < poolSize; i++ {
			cli, err := gokalkan.NewClient(clientPool.opts...)
			if err != nil {
				// If we can't create initial clients, fall back to on-demand creation
				break
			}
			clientPool.pool <- cli
		}
	})
	return clientPool
}

// getClient gets a client from the pool or creates a new one if pool is empty
func (p *KalkanClientPool) getClient() (*gokalkan.Client, error) {
	select {
	case cli := <-p.pool:
		return cli, nil
	default:
		// Pool is empty, create a new client
		return gokalkan.NewClient(p.opts...)
	}
}

// putClient returns a client to the pool or closes it if pool is full
func (p *KalkanClientPool) putClient(cli *gokalkan.Client) {
	select {
	case p.pool <- cli:
		// Successfully returned to pool
	default:
		// Pool is full, close the client
		cli.Close()
	}
}

// CloseAll closes all clients in the pool (call this on application shutdown)
func (p *KalkanClientPool) CloseAll() {
	close(p.pool)
	for cli := range p.pool {
		cli.Close()
	}
}

func (u *User) SignXML(xmlData string) (string, error) {
	// Initialize pool with 3 clients (adjust based on your needs)
	pool := initClientPool(3)

	// Get a client from the pool
	cli, err := pool.getClient()
	if err != nil {
		return "", fmt.Errorf("create kalkan client error: %w", err)
	}
	defer pool.putClient(cli) // Return client to pool when done

	err = cli.LoadKeyStore(u.CertPath, u.CertPassword)
	if err != nil {
		return "", fmt.Errorf("load key store error: %w", err)
	}
	signedXML, err := cli.SignXML("<data>" + xmlData + "</data>")
	if err != nil {
		return "", fmt.Errorf("sign xml error: %w", err)
	}

	// remove id attribute from signed xml
	idx := strings.Index(signedXML, ` Id="1"`)
	if idx != -1 {
		signedXML = signedXML[:idx] + signedXML[idx+7:]
	}
	return signedXML, nil
}
