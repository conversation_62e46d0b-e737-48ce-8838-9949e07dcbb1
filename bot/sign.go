package bot

import (
	"fmt"
	"strings"
	"sync"

	"github.com/gokalkan/gokalkan"
)

// Global mutex to serialize ALL access to KalkanCrypt library
// The underlying C library is NOT thread-safe and causes segfaults with concurrent access
var kalkanMutex sync.Mutex

// Single client instance that will be reused (safer than creating multiple instances)
var kalkanClient *gokalkan.Client
var kalkanOnce sync.Once
var kalkanErr error

// initKalkanClient initializes a single kalkan client for reuse
func initKalkanClient() (*gokalkan.Client, error) {
	kalkanOnce.Do(func() {
		opts := gokalkan.OptsProd
		kalkanClient, kalkanErr = gokalkan.NewClient(opts...)
		if kalkanErr == nil {
			fmt.Println("Kalkan client initialized successfully (thread-safe mode)")
		}
	})
	return kalkanClient, kalkanErr
}

func (u *User) SignXML(xmlData string) (string, error) {
	// CRITICAL: Lock the entire operation to prevent segfaults
	// The KalkanCrypt C library is not thread-safe at all
	kalkanMutex.Lock()
	defer kalkanMutex.Unlock()

	// Get the shared client instance
	cli, err := initKalkanClient()
	if err != nil {
		return "", fmt.Errorf("init kalkan client error: %w", err)
	}

	// Load the user's specific key
	err = cli.LoadKeyStore(u.CertPath, u.CertPassword)
	if err != nil {
		return "", fmt.Errorf("load key store error: %w", err)
	}
	signedXML, err := cli.SignXML("<data>" + xmlData + "</data>")
	if err != nil {
		return "", fmt.Errorf("sign xml error: %w", err)
	}

	// remove id attribute from signed xml
	idx := strings.Index(signedXML, ` Id="1"`)
	if idx != -1 {
		signedXML = signedXML[:idx] + signedXML[idx+7:]
	}
	return signedXML, nil
}
