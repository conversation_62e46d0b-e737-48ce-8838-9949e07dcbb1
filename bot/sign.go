package bot

import (
	"fmt"
	"strings"
	"sync"

	"github.com/gokalkan/gokalkan"
)

// Global mutex to serialize access to KalkanCrypt library
// The underlying KalkanCrypt library is not thread-safe
var kalkanMutex sync.Mutex

func (u *User) SignXML(xmlData string) (string, error) {
	// Lock to ensure only one goroutine uses KalkanCrypt at a time
	kalkanMutex.Lock()
	defer kalkanMutex.Unlock()

	// Create a new client instance for each user
	opts := gokalkan.OptsProd
	cli, err := gokalkan.NewClient(opts...)
	if err != nil {
		return "", fmt.Errorf("create kalkan client error: %w", err)
	}
	defer cli.Close() // Important: close the client to free resources

	err = cli.LoadKeyStore(u.CertPath, u.CertPassword)
	if err != nil {
		return "", fmt.Errorf("load key store error: %w", err)
	}
	signedXML, err := cli.SignXML("<data>" + xmlData + "</data>")
	if err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("sign xml error: %w", err)
	}

	// remove id attribute from signed xml
	idx := strings.Index(signedXML, ` Id="1"`)
	if idx != -1 {
		signedXML = signedXML[:idx] + signedXML[idx+7:]
	}
	return signedXML, nil
}
