package bot

import (
	"fmt"
	"strings"

	"github.com/gokalkan/gokalkan"
)

package bot

import (
	"fmt"
	"strings"
	"sync"

	"github.com/gokalkan/gokalkan"
)

var (
	kalkanClient *gokalkan.Client
	kalkanOnce   sync.Once
	kalkanErr    error
)

func getKalkanClient() (*gokalkan.Client, error) {
	kalkanOnce.Do(func() {
		opts := gokalkan.OptsProd
		kalkanClient, kalkanErr = gokalkan.NewClient(opts...)
	})
	return kalkanClient, kalkanErr
}

func (u *User) SignXML(xmlData string) (string, error) {
	cli, err := getKalkanClient()
	if err != nil {
		return "", fmt.Errorf("get kalkan client error: %w", err)
	}

	err = cli.LoadKeyStore(u.CertPath, u.CertPassword)
	if err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("load key store error: %w", err)
	}
	signedXML, err := cli.SignXML("<data>" + xmlData + "</data>")
	if err != nil {
		return "", fmt.Errorf("sign xml error: %w", err)
	}

	// remove id attribute from signed xml
	idx := strings.Index(signedXML, ` Id="1"`)
	if idx != -1 {
		signedXML = signedXML[:idx] + signedXML[idx+7:]
	}
	return signedXML, nil
}
