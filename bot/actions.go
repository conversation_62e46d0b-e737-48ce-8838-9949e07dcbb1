package bot

import (
	"bytes"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"net/url"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
)

const (
	loginURL    = "https://balabaqsha.bilimalmaty.kz/Client"
	genTokenURL = "https://api.auth.bilimalmaty.kz/api/auth/generateToken"
	certInfoURL = "https://api.auth.bilimalmaty.kz/api/auth/certInfo"
	authURL     = "https://api.auth.bilimalmaty.kz/api/auth/"
	gardensURL  = "https://balabaqsha.bilimalmaty.kz/DayCareQueue/Generic/DayCareChoise"
	toXMLURL    = "https://balabaqsha.bilimalmaty.kz/DayCareQueue/Generic/ToXml"
	executeURL  = "https://balabaqsha.bilimalmaty.kz/DayCareQueue/Generic/ExecuteDayCareChoise"
)

func (u *User) Login() error {
	req := u.Client.NewGetRequest(loginURL)
	defer fasthttp.ReleaseRequest(req)
	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	if err := u.Client.Do(req, resp); err != nil {
		return fmt.Errorf("initial get request failed: %v", err)
	}
	respURL := req.URI().String() // Need for later requests

	parsedURL, err := url.Parse(respURL)
	if err != nil {
		return fmt.Errorf("error parsing response URL: %v", err)
	}
	returnURL := parsedURL.Query().Get("ReturnUrl")

	// 2 - Get token & sign XML
	body, err := u.sendGetRequest(genTokenURL)
	if err != nil {
		return fmt.Errorf("sendGetRequest genTokenURL err: %v", err)
	}
	var tokenResponse map[string]string
	json.Unmarshal(body, &tokenResponse)
	token := tokenResponse["token"]
	signedXML, err := u.SignXML(token)
	if err != nil {
		return fmt.Errorf("SignXML err: %v", err)
	}

	// 3 - Send signed XML
	loginFormData := map[string]string{
		"Xml": signedXML,
	}
	jsonData, err := json.Marshal(loginFormData)
	if err != nil {
		return fmt.Errorf("error marshaling JSON (loginFormData): %v", err)
	}
	body, err = u.sendJsonRequest(certInfoURL, "", jsonData)
	if err != nil {
		return fmt.Errorf("sendJsonRequest certInfoURL err: %v", err)
	}

	// 4 - Get user data
	err = json.Unmarshal(body, &u.ChildInfo)
	if err != nil {
		return fmt.Errorf("error unmarshaling JSON (ChildInfo): %v", err)
	}

	authFormData := map[string]string{
		"userName":  u.ChildInfo.IIN,
		"password":  "random",
		"email":     "",
		"signedXml": signedXML,
		"fullName":  u.ChildInfo.FullName,
		"returnUrl": returnURL,
	}
	jsonForm, err := json.Marshal(authFormData)
	if err != nil {
		return fmt.Errorf("error marshaling JSON (authFormData): %v", err)
	}

	_, err = u.sendJsonRequest(authURL, "", jsonForm)
	if err != nil {
		return fmt.Errorf("sendJsonRequest authURL err: %v", err)
	}
	// CALLBACK
	body, _ = u.sendGetRequest(returnURL)
	accessFormData, err := ExtractFormData(string(body))
	if err != nil {
		return fmt.Errorf("error extracting form data (accessFormData): %v", err)
	}
	u.sendFormRequest("https://balabaqsha.bilimalmaty.kz/", "", accessFormData)
	fmt.Printf("User - %d, Login Status:%d, %s\n", u.ID, resp.StatusCode(), req.URI())
	body1, _ := u.sendGetRequest("https://balabaqsha.bilimalmaty.kz/Client/Cabinet/List")
	u.saveResponseToFile("cabinett.html", body1)

	return nil
}

func (u *User) AttemptGardenBooking() error {
	// Fetch garden list
	gardenData, err := u.fetchGardenList()
	if err != nil {
		return fmt.Errorf("failed to fetch garden list: %v", err)
	}

	// Get signature for booking
	sign, err := u.prepareSignatureForBooking(gardenData)
	if err != nil {
		return fmt.Errorf("failed to prepare signature for booking: %v", err)
	}

	// Process garden data and attempt to book a place
	return u.BookPlace(gardenData, sign)
}

func (u *User) fetchGardenList() ([]byte, error) {
	gardenFormData := map[string]string{
		"requestId":              u.RequestID,
		"transition[name]":       "DayCareChoise",
		"transition[definition]": definition,
		"transition[btnName]":    btnName,
		"transition[awareText]":  "",
		"transition[hasAware]":   "false",
		"definition":             definition,
	}

	body, _ := u.sendFormRequest(gardensURL, "", gardenFormData)
	gardenList := ExtractGardens(body)

	// Retry if garden list is empty
	attempts := 0
	for gardenList == nil && attempts < 5 {
		fmt.Printf("User - %d: garden list is empty, retry after 0.5 second ...\n", u.ID)
		time.Sleep(500 * time.Millisecond)
		body, _ = u.sendFormRequest(gardensURL, "", gardenFormData)
		gardenList = ExtractGardens(body)
		attempts++
	}

	if gardenList == nil {
		return nil, fmt.Errorf("garden list is still empty after 5 attempts")
	}

	// Return a copy of the garden list to avoid modifying the original
	gardenData := make([]byte, len(gardenList))
	copy(gardenData, gardenList)
	return gardenData, nil
}

func (u *User) prepareSignatureForBooking(gardenData []byte) (string, error) {
	// Decode the extracted JSON data into a map
	var dataMap map[string]interface{}
	if err := json.Unmarshal(gardenData, &dataMap); err != nil {
		return "", fmt.Errorf("error unmarshaling garden data: %v", err)
	}

	wrappedData := map[string]interface{}{
		"requestId":  u.RequestID,
		"transition": dataMap,
	}

	// Convert the wrapped data to JSON
	jsonData, err := json.Marshal(wrappedData)
	if err != nil {
		return "", fmt.Errorf("error marshaling wrapped data: %v", err)
	}

	// Create a new multipart writer
	var reqBody bytes.Buffer
	writer := multipart.NewWriter(&reqBody)

	// Create a form field and write the JSON data to it
	part, err := writer.CreateFormField("json")
	if err != nil {
		return "", fmt.Errorf("error creating multipart form field: %v", err)
	}
	if _, err := part.Write(jsonData); err != nil {
		return "", fmt.Errorf("error writing JSON to form field: %v", err)
	}

	// Close the writer to finalize the form data
	if err := writer.Close(); err != nil {
		fmt.Printf("User - %d, error closing writer: %v\nGoing further --> ", u.ID, err)
	}

	req := u.Client.NewPostRequest(toXMLURL)
	defer fasthttp.ReleaseRequest(req)
	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	req.Header.SetContentType(writer.FormDataContentType())
	req.SetBody(reqBody.Bytes())

	// Perform the request
	if err := u.Client.Do(req, resp); err != nil {
		return "", fmt.Errorf("error performing XML request: %v", err)
	}

	fmt.Printf("User - %d, Garden List Status:%d, %s\n", u.ID, resp.StatusCode(), req.URI())
	u.saveResponseToFile("gardenList.html", resp.Body())

	// Sign the XML
	sign, err := u.SignXML(string(resp.Body()))
	if err != nil {
		return "", fmt.Errorf("error signing XML: %v", err)
	}

	return sign, nil
}

func (u *User) BookPlace(gardenData []byte, signedXml string) error {
	var response Response
	if err := json.Unmarshal(gardenData, &response); err != nil {
		return fmt.Errorf("error unmarshaling garden data: %v", err)
	}

	// Find target garden
	var theGarden DayCareFreePlace
	found := false
	for _, place := range response.DayCareFreePlaces {
		if strings.Contains(place.Title, u.GardenName) {
			theGarden = place
			found = true
			break
		}
	}

	if !found {
		return fmt.Errorf("garden '%s' not found in the available list", u.GardenName)
	}

	// Prepare form data for booking
	var body bytes.Buffer
	writer := multipart.NewWriter(&body)

	// Add common form fields
	_ = writer.WriteField("requestId", u.RequestID)
	_ = writer.WriteField("freePlaceId", "undefined")
	_ = writer.WriteField("definition", response.Definition)
	_ = writer.WriteField("transition.Definition", response.Definition)
	_ = writer.WriteField("transition.ActionName", response.ActionName)
	_ = writer.WriteField("transition.RequestId", u.RequestID)
	_ = writer.WriteField("transition.ForTwins", fmt.Sprintf("%t", response.ForTwins))
	_ = writer.WriteField("transition.Name", response.Name)
	_ = writer.WriteField("transition.Signed.xmlString", signedXml)
	_ = writer.WriteField("transition.Signed.SigningType", "0")
	_ = writer.WriteField("transition.AwareText", response.AwareText)

	// Add all available garden places
	for i, place := range response.DayCareFreePlaces {
		writeFormField(writer, fmt.Sprintf("transition.DayCareFreePlaces[%d].Id", i), fmt.Sprintf("%d", place.Id))
		writeFormField(writer, fmt.Sprintf("transition.DayCareFreePlaces[%d].Code", i), place.Code)
		writeFormField(writer, fmt.Sprintf("transition.DayCareFreePlaces[%d].KindergartenId", i), fmt.Sprintf("%d", place.KindergartenId))
		writeFormField(writer, fmt.Sprintf("transition.DayCareFreePlaces[%d].DayCareType", i), fmt.Sprintf("%d", place.DayCareType))
		writeFormField(writer, fmt.Sprintf("transition.DayCareFreePlaces[%d].DayCareTypeText", i), place.DayCareTypeText)
		writeFormField(writer, fmt.Sprintf("transition.DayCareFreePlaces[%d].DayCareWebSite", i), place.DayCareWebSite)
		writeFormField(writer, fmt.Sprintf("transition.DayCareFreePlaces[%d].Title", i), place.Title)
		writeFormField(writer, fmt.Sprintf("transition.DayCareFreePlaces[%d].Address", i), place.Address)
		writeFormField(writer, fmt.Sprintf("transition.DayCareFreePlaces[%d].Phone", i), place.Phone)
		writeFormField(writer, fmt.Sprintf("transition.DayCareFreePlaces[%d].Language", i), fmt.Sprintf("%d", place.Language))
		writeFormField(writer, fmt.Sprintf("transition.DayCareFreePlaces[%d].LanguageName", i), place.LanguageName)
		writeFormField(writer, fmt.Sprintf("transition.DayCareFreePlaces[%d].FreeCount", i), fmt.Sprintf("%d", place.FreeCount))
		writeFormField(writer, fmt.Sprintf("transition.DayCareFreePlaces[%d].PlaceKind", i), fmt.Sprintf("%d", place.PlaceKind))
		writeFormField(writer, fmt.Sprintf("transition.DayCareFreePlaces[%d].PlaceAvailabilityDate", i), place.PlaceAvailabilityDate)
		writeFormField(writer, fmt.Sprintf("transition.DayCareFreePlaces[%d].AdditionalProjectCapacity", i), fmt.Sprintf("%d", place.AdditionalProjectCapacity))
	}

	// Add selected garden information
	_ = writer.WriteField("transition.FreePlaceId", "undefined")
	_ = writer.WriteField("transition.FreePlaceType", "0")
	_ = writer.WriteField("transition.SelectedDayCare.Id", fmt.Sprintf("%d", theGarden.Id))
	_ = writer.WriteField("transition.SelectedDayCare.Code", theGarden.Code)
	_ = writer.WriteField("transition.SelectedDayCare.KindergartenId", fmt.Sprintf("%d", theGarden.KindergartenId))
	_ = writer.WriteField("transition.SelectedDayCare.DayCareType", fmt.Sprintf("%d", theGarden.DayCareType))
	_ = writer.WriteField("transition.SelectedDayCare.DayCareTypeText", theGarden.DayCareTypeText)
	_ = writer.WriteField("transition.SelectedDayCare.DayCareWebSite", theGarden.DayCareWebSite)
	_ = writer.WriteField("transition.SelectedDayCare.Title", theGarden.Title)
	_ = writer.WriteField("transition.SelectedDayCare.Address", theGarden.Address)
	_ = writer.WriteField("transition.SelectedDayCare.Phone", theGarden.Phone)
	_ = writer.WriteField("transition.SelectedDayCare.Language", fmt.Sprintf("%d", theGarden.Language))
	_ = writer.WriteField("transition.SelectedDayCare.LanguageName", theGarden.LanguageName)
	_ = writer.WriteField("transition.SelectedDayCare.FreeCount", fmt.Sprintf("%d", theGarden.FreeCount))
	_ = writer.WriteField("transition.SelectedDayCare.PlaceKind", fmt.Sprintf("%d", theGarden.PlaceKind))
	_ = writer.WriteField("transition.SelectedDayCare.PlaceAvailabilityDate", theGarden.PlaceAvailabilityDate)
	_ = writer.WriteField("transition.SelectedDayCare.SpecialCategoryName", "")
	_ = writer.WriteField("transition.SelectedDayCare.AdditionalProjectCapacity", fmt.Sprintf("%d", theGarden.AdditionalProjectCapacity))
	_ = writer.WriteField("transition.SelectedDayCare.PlaceKindDescription", "жалпыға бірдей белгіленген")
	_ = writer.WriteField("forTwins", "false")

	// Close the writer to finalize the form data
	writer.Close()

	return u.executeBooking(body.Bytes(), writer.FormDataContentType())
}

func (u *User) executeBooking(formData []byte, contentType string) error {
	// Aggressive retry strategy for competitive booking
	maxRetries := 5                     // More retries for better success rate
	retryDelay := 50 * time.Millisecond // Faster initial retry

	for attempt := 0; attempt < maxRetries; attempt++ {
		req := u.Client.NewPostRequest(executeURL)
		defer fasthttp.ReleaseRequest(req)
		resp := fasthttp.AcquireResponse()
		defer fasthttp.ReleaseResponse(resp)

		req.Header.SetContentType(contentType)
		req.SetBody(formData)

		start := time.Now()
		err := u.Client.Do(req, resp)
		requestDuration := time.Since(start)

		if err != nil {
			fmt.Printf("🔄 User %d, Attempt %d: Request error in %v: %v\n", u.ID, attempt+1, requestDuration, err)
			if attempt < maxRetries-1 { // Don't sleep on last attempt
				time.Sleep(retryDelay)
				retryDelay = time.Duration(float64(retryDelay) * 1.5) // Gentler backoff
			}
			continue
		}

		fmt.Printf("📡 User %d, Attempt %d: Response in %v, Status: %d\n",
			u.ID, attempt+1, requestDuration, resp.StatusCode())

		var res Result
		err = json.Unmarshal(resp.Body(), &res)

		if err != nil {
			fmt.Printf("⚠️  User %d, Attempt %d: Parse error: %v\n", u.ID, attempt+1, err)
			if attempt < maxRetries-1 {
				time.Sleep(retryDelay)
				retryDelay = time.Duration(float64(retryDelay) * 1.5)
			}
			continue
		}

		if res.IsSuccess {
			fmt.Printf("🎉 User %d: BOOKING SUCCESS on attempt %d in %v!\n", u.ID, attempt+1, requestDuration)
			return nil // Success
		}

		fmt.Printf("❌ User %d, Attempt %d: Booking failed, retrying...\n", u.ID, attempt+1)
		if attempt < maxRetries-1 {
			time.Sleep(retryDelay)
			retryDelay = time.Duration(float64(retryDelay) * 1.5)
		}
	}

	return fmt.Errorf("failed to book place after %d attempts", maxRetries)
}
